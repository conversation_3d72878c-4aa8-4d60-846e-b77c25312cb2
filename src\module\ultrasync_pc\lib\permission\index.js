/**
 * 权限管理器入口文件
 * 提供统一的权限管理接口
 */

import permissionManager, { PermissionManager } from './PermissionManager.js';
import BasePermissionManager from './BasePermissionManager.js';
import RoutePermissionManager from './RoutePermissionManager.js';
import ComponentPermissionManager from './ComponentPermissionManager.js';
import FeaturePermissionManager from './FeaturePermissionManager.js';

// 导出主要的权限管理器实例
export default permissionManager;

// 导出所有类，供需要自定义的场景使用
export {
    PermissionManager,
    BasePermissionManager,
    RoutePermissionManager,
    ComponentPermissionManager,
    FeaturePermissionManager
};

// 便捷方法导出
export const {
    checkPermission,
    checkRoutePermission,
    checkComponentPermission,
    checkFeaturePermission,
    checkRegionPermission,
    checkApiPermission,
    checkDataPermission,
    checkElementPermission,
    isComponentVisible,
    isComponentDisabled,
    getAccessibleRoutes,
    getAvailableActions,
    getRedirectRoute,
    batchCheckPermissions,
    updateUserInfo,
    clearCache,
    getUserInfo,
    getUserRole,
    getUserId,
    isAdmin,
    isSuperAdmin,
    isInitialized,
    initialize,
    initializeRegionPermissions,
    destroy,
    isRegionFunctionEnabled,
    getEnabledRegionFunctions,
    getCurrentRegion,
    isRegionFunctionAvailable,
    getRegionMappedPermissions,
    checkAllRegionMappedPermissions,
    getAllEnabledMappedPermissions,
    getRegionPermissionMappingSummary
} = permissionManager;

/**
 * Vue插件安装函数
 * @param {Object} Vue - Vue构造函数
 * @param {Object} options - 插件选项
 */
export function install(Vue, options = {}) {
    // 将权限管理器添加到Vue原型
    Vue.prototype.$permission = permissionManager;

    // 添加全局混入，提供权限检查方法
    Vue.mixin({
        data() {
            return {
                // 响应式的权限管理器初始化状态
                $isPermissionInitialized: permissionManager.isInitialized(),
                $isRegionPermissionInitialized: permissionManager.regionInitialized
            };
        },

        created() {
            // 监听权限管理器初始化事件
            const updateInitStatus = () => {

                this.$isPermissionInitialized = permissionManager.isInitialized();
                this.$isRegionPermissionInitialized = permissionManager.regionInitialized;
                console.error('updateInitStatus', this.$isPermissionInitialized);
            };

            // 监听初始化完成事件
            window.addEventListener('permission:initialized', updateInitStatus);
            window.addEventListener('permission:regionInitialized', updateInitStatus);

            // 组件销毁时移除监听器
            this.$once('hook:beforeDestroy', () => {
                window.removeEventListener('permission:initialized', updateInitStatus);
                window.removeEventListener('permission:regionInitialized', updateInitStatus);
            });
        },

        methods: {
            // 通用权限检查方法
            $checkPermission(permission, context = {}) {
                return permissionManager.checkPermission(permission, context);
            },

            // 检查路由权限
            $checkRoute(routePath, context = {}) {
                return permissionManager.checkRoutePermission(routePath, context);
            },

            // 检查组件权限
            $checkComponent(component, action = null, context = {}) {
                return permissionManager.checkComponentPermission(component, action, context);
            },

            // 检查功能权限
            $checkFeature(feature, action = null, context = {}) {
                return permissionManager.checkFeaturePermission(feature, action, context);
            },

            // 检查区域功能权限
            $checkRegionFunction(functionName, context = {}) {
                return permissionManager.checkRegionPermission(functionName, context);
            },

            // 检查区域功能是否启用
            $isRegionFunctionEnabled(functionName) {
                return permissionManager.isRegionFunctionEnabled(functionName);
            },

            // 检查功能在当前区域是否可用
            $isRegionFunctionAvailable(functionName, options = {}) {
                return permissionManager.isRegionFunctionAvailable(functionName, options);
            },

            // 获取当前区域
            $getCurrentRegion() {
                return permissionManager.getCurrentRegion();
            },

            // 获取指定区域功能的所有映射权限
            $getRegionMappedPermissions(regionFunction) {
                return permissionManager.getRegionMappedPermissions(regionFunction);
            },

            // 检查指定区域功能的所有映射权限
            $checkAllRegionMappedPermissions(regionFunction) {
                return permissionManager.checkAllRegionMappedPermissions(regionFunction);
            },

            // 获取所有启用的映射权限
            $getAllEnabledMappedPermissions() {
                return permissionManager.getAllEnabledMappedPermissions();
            },

            // 获取权限映射关系摘要
            $getRegionPermissionMappingSummary() {
                return permissionManager.getRegionPermissionMappingSummary();
            },

            // 检查是否为管理员
            $isAdmin() {
                return permissionManager.isAdmin();
            },

            // 检查是否为超级管理员
            $isSuperAdmin() {
                return permissionManager.isSuperAdmin();
            },

            // 获取用户角色
            $getUserRole() {
                return permissionManager.getUserRole();
            },
        }
    });

    // 添加全局指令 v-permission
    Vue.directive('permission', {
        bind(el, binding, vnode) {
            const { value, modifiers } = binding;

            if (!value) {
                console.warn('v-permission directive requires a value');
                return;
            }

            let hasPermission = false;

            if (typeof value === 'string') {
                // 简单权限检查
                if (modifiers.route) {
                    hasPermission = permissionManager.checkRoutePermission(value);
                } else if (modifiers.component) {
                    hasPermission = permissionManager.checkComponentPermission(value);
                } else if (modifiers.feature) {
                    hasPermission = permissionManager.checkFeaturePermission(value);
                } else if (modifiers.region) {
                    hasPermission = permissionManager.checkRegionPermission(value);
                } else {
                    // 默认检查通用权限（包括区域权限）
                    hasPermission = permissionManager.checkPermission(value);
                }
            } else if (typeof value === 'object') {
                // 复杂权限检查
                const { type, permission, action, context, regionPermissionKey, featurePermissionKey } = value;

                // 如果提供了regionPermissionKey或featurePermissionKey，使用通用权限检查
                if (regionPermissionKey || featurePermissionKey) {
                    hasPermission = permissionManager.checkPermission({
                        regionPermissionKey,
                        featurePermissionKey
                    }, context || {});
                } else {
                    // 原有的类型化权限检查
                    switch (type) {
                    case 'route':
                        hasPermission = permissionManager.checkRoutePermission(permission, context);
                        break;
                    case 'component':
                        hasPermission = permissionManager.checkComponentPermission(permission, action, context);
                        break;
                    case 'feature':
                        hasPermission = permissionManager.checkFeaturePermission(permission, action, context);
                        break;
                    case 'api':
                        hasPermission = permissionManager.checkApiPermission(permission, action, context);
                        break;
                    case 'data':
                        hasPermission = permissionManager.checkDataPermission(permission, action, context);
                        break;
                    default:
                        hasPermission = permissionManager.checkFeaturePermission(permission, action, context);
                    }
                }
            }

            // 根据权限结果处理元素
            if (!hasPermission) {
                if (modifiers.hide) {
                    // 隐藏元素
                    el.style.display = 'none';
                } else if (modifiers.disable) {
                    // 禁用元素
                    el.disabled = true;
                    el.classList.add('disabled');
                } else {
                    // 默认移除元素
                    el.parentNode && el.parentNode.removeChild(el);
                }
            }
        },

        update(el, binding) {
            // 权限变化时重新检查
            this.bind(el, binding);
        }
    });

    // 如果提供了初始化选项，自动初始化
    if (options.autoInit && options.userInfo) {
        permissionManager.initialize(options.userInfo, options.config || {});
    }
}

// 自动安装（如果在浏览器环境中且Vue可用）
if (typeof window !== 'undefined' && window.Vue) {
    install(window.Vue);
}
